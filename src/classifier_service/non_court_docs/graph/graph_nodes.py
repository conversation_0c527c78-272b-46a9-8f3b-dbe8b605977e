from langgraph.graph import StateGraph

from src.classifier_service.common.graph_state import DocumentState
from src.classifier_service.non_court_docs.consts.rule_based_engine import rule_based_classifier
from src.classifier_service.non_court_docs.graph.correction_node_non_court import \
    non_court_correction_node_with_double_check
from src.classifier_service.non_court_docs.graph.graph_edges import rule_based_router, route_document_by_general_type, \
    financial_type_router, insurance_type_router, medical_type_router, case_management_router, \
    add_final_node_routing_to_correction
from src.classifier_service.non_court_docs.graph.nodes_registry import general_type_classifier, \
    financial_type_classifier, insurance_type_classifier, medical_type_classifier, government_agency_classifier, \
    case_claim_management_classifier, general_correspondence_classifier, banking_and_accounting_classifier, \
    loan_and_credits_classifier, investment_and_retirement_classifier, income_and_benefits_classifier, \
    billing_invoices_payments_classifier, property_and_asset_management_classifier, \
    policy_details_and_coverage_type_classifier, claims_process_and_communication_classifier, \
    gov_benefit_administration_classifier, medical_records_evaluation_classifier, healthcare_finance_claims_classifier, \
    client_profile_classifier, case_management_classifier, case_resolution_classifier, formal_legal_admin_classifier, \
    notice_classifier

builder = StateGraph(DocumentState)

# Add pre-classifier rule-based node
builder.add_node("rule_based_classifier", rule_based_classifier)
# Add first Node
builder.add_node("general_type_classification", general_type_classifier)

# Add Subcategories
builder.add_node("financial_documents_classification", financial_type_classifier)
builder.add_node("insurance_documents_classification", insurance_type_classifier)
builder.add_node("medical_type_classifier", medical_type_classifier)
builder.add_node("government_agency_classifier", government_agency_classifier)
builder.add_node("case_claim_management_classifier", case_claim_management_classifier)
builder.add_node("general_correspondence_classifier", general_correspondence_classifier)

# Add financial and banking nodes
builder.add_node("banking_and_accounting_doc_type", banking_and_accounting_classifier)
builder.add_node("loans_and_credits_doc_type", loan_and_credits_classifier)
builder.add_node("investment_and_retirement_doc_type", investment_and_retirement_classifier)
builder.add_node("income_and_benefits_doc_type", income_and_benefits_classifier)
builder.add_node("billing_invoices_payments_doc_type", billing_invoices_payments_classifier)
builder.add_node("property_and_asset_management_doc_type", property_and_asset_management_classifier)

# Insurance subtypes  - Combine into one and test
# builder.add_node("policy_details_and_coverage_type",policy_details_and_coverage_type_classifier)
# builder.add_node("claims_process_and_communication_type", claims_process_and_communication_classifier)

# Medical
builder.add_node("gov_benefit_administration_classifier", gov_benefit_administration_classifier)
builder.add_node("medical_records_evaluation_classifier", medical_records_evaluation_classifier)
builder.add_node("healthcare_finance_claims_classifier", healthcare_finance_claims_classifier)

# Case Management
builder.add_node("client_profile_classifier", client_profile_classifier)
builder.add_node("case_management_classifier", case_management_classifier)
builder.add_node("case_resolution_classifier", case_resolution_classifier)
builder.add_node("formal_legal_admin_classifier", formal_legal_admin_classifier)

# Notice
builder.add_node("notice_classifier", notice_classifier)

# Self Correction
builder.add_node("self_correction", non_court_correction_node_with_double_check())

# Add Edge
builder.add_edge("notice_classifier", "__end__")

# Add conditional routing
builder.add_conditional_edges("rule_based_classifier", rule_based_router)
builder.add_conditional_edges("general_type_classification", route_document_by_general_type)
builder.add_conditional_edges("financial_documents_classification", financial_type_router)
# builder.add_conditional_edges("insurance_documents_classification", insurance_type_router)
builder.add_conditional_edges("medical_type_classifier", medical_type_router)
builder.add_conditional_edges("case_claim_management_classifier", case_management_router)

# Set entry point

builder.set_entry_point("rule_based_classifier")

leaf_nodes = [
    # Financial
    "banking_and_accounting_doc_type",
    "loans_and_credits_doc_type",
    "investment_and_retirement_doc_type",
    "income_and_benefits_doc_type",
    "billing_invoices_payments_doc_type",
    "property_and_asset_management_doc_type",

    # Insurance
    "policy_details_and_coverage_type",
    "claims_process_and_communication_type",

    # Medical
    "gov_benefit_administration_classifier",
    "medical_records_evaluation_classifier",
    "healthcare_finance_claims_classifier",

    # Government & Agency
    "government_agency_classifier",

    # Case & Claim Management
    "client_profile_classifier",
    "case_management_classifier",
    "case_resolution_classifier",
    "formal_legal_admin_classifier",

    # General Communication & Administrative
    "general_correspondence_classifier"
    
    # Add insurance node - TODO Testing
    "insurance_documents_classification"
]

# Add final node routing to self-correction
add_final_node_routing_to_correction(builder, leaf_nodes)

builder.set_finish_point("self_correction")

non_court_document_classifier_graph = builder.compile()

# # Print the graph for debugging
# graph = non_court_document_classifier_graph.get_graph()
# print("Nodes:")
# for node in graph.nodes:
#     print("-", node)
#
# print("\nEdges:")
# for edge in graph.edges:
#     print("-", edge)
