"""
Enhanced Prompt Factory for Non-Court Documents

This module generates classification prompts using JSON knowledge bases where available,
with fallback to enum-based prompts. Provides rich context with detailed definitions
and usage guidelines for improved classification accuracy.
"""

from src.classifier_service.common.prompt_generator import generate_category_prompt, generate_enhanced_category_prompt
from src.classifier_service.common.knowledge_loader import get_glossary_path
from src.classifier_service.non_court_docs.consts.case_claim import CaseClaimSubType, ClientIntakeAndProfile, \
    CaseManagementAndWorkflow, ClaimsResolutionsAndAgreements, FormalLegalAndSpecializedAdmin
from src.classifier_service.non_court_docs.consts.category_descriptions import financial_type_descriptions, \
    general_type_descriptions, billing_docs_descriptions, insurance_subtype_descriptions, \
    health_medical_subtype_descriptions, case_claim_subtype_descriptions
from src.classifier_service.non_court_docs.consts.financial import FinancialTypes, BankingAndAccounts, \
    LoansAndCredit, InvestmentsAndRetirement, IncomeAndBenefitsFinancial, BillingInvoicesPayments, \
    PropertyAndAssetManagement
from src.classifier_service.non_court_docs.consts.general_correspondance import GeneralTypeClassififcation, \
    GeneralCommunicationAndAdministrativeDocuments
from src.classifier_service.non_court_docs.consts.government import GovernmentAndAgencyDocuments
from src.classifier_service.non_court_docs.consts.insurance import InsuranceSubType, PolicyDetailsAndCoverage, \
    ClaimsProcessAndCommunication
from src.classifier_service.non_court_docs.consts.medical import HealthMedicalSubType, GovernmentBenefitAdministration, \
    MedicalRecordsAndEvaluations, HealthcareFinanceAndClaims
from src.classifier_service.non_court_docs.consts.notices import NoticeDocuments
from src.classifier_service.non_court_docs.prompts.base_prompts import base_non_court_classification_prompt


def create_enhanced_prompt(json_path, enum_fallback, category_name, category_description=None, 
                          include_definitions=True, include_usage_notes=True, max_definitions=None):
    """
    Create enhanced prompt with JSON knowledge base or fallback to enum.
    
    Args:
        json_path: Path to JSON knowledge base file
        enum_fallback: Enum class to use as fallback
        category_name: Name of the category
        category_description: Optional description for enum fallback
        include_definitions: Whether to include detailed definitions
        include_usage_notes: Whether to include usage guidelines
        max_definitions: Maximum number of definitions to include
    
    Returns:
        Generated prompt string
    """
    try:
        return generate_enhanced_category_prompt(
            base_non_court_classification_prompt,
            json_path,
            category_name,
            include_definitions=include_definitions,
            include_usage_notes=include_usage_notes,
            max_definitions=max_definitions
        )
    except Exception as e:
        print(f"Warning: Could not load enhanced prompt for {category_name}: {e}")
        print(f"Falling back to enum-based prompt for {category_name}")
        return generate_category_prompt(
            base_non_court_classification_prompt,
            enum_fallback,
            category_name,
            category_description
        )


# =============================================================================
# GENERAL DOCUMENT CLASSIFICATION PROMPTS
# =============================================================================

# General Document Types - Enhanced with JSON knowledge base
try:
    general_type_classification_prompt = generate_enhanced_category_prompt(
        base_non_court_classification_prompt,
        get_glossary_path("general_correspondance", "general_communication_administrative_definitions.json"),
        "General Document Types",
        include_definitions=True,
        include_usage_notes=True
    )
except Exception:
    general_type_classification_prompt = generate_category_prompt(
        base_non_court_classification_prompt, 
        GeneralTypeClassififcation, 
        "General Document Types", 
        general_type_descriptions
    )
# print(f"General Type Classification Prompt:\n{general_type_classification_prompt}\n" + "="*80)


# =============================================================================
# FINANCIAL AND BANKING DOCUMENT CLASSIFICATION PROMPTS
# =============================================================================

# Financial Document Types - Using enum (no JSON knowledge base yet)
financial_type_classification_prompt = generate_category_prompt(
    base_non_court_classification_prompt, 
    FinancialTypes, 
    "Financial Document Types", 
    financial_type_descriptions
)
# print(f"Financial Type Classification Prompt:\n{financial_type_classification_prompt}\n" + "="*80)

# Banking and Accounting - Enhanced with JSON knowledge base
banking_and_accounting_classification_prompt = create_enhanced_prompt(
    get_glossary_path("financial", "banking_accounts_definitions.json"),
    BankingAndAccounts,
    "Banking and Accounting Document Types",
    include_definitions=True,
    include_usage_notes=True
)
# print(f"Banking and Accounting Classification Prompt:\n{banking_and_accounting_classification_prompt}\n" + "="*80)

# Loans and Credit - Enhanced with JSON knowledge base
loan_and_credits_type_classification_prompt = create_enhanced_prompt(
    get_glossary_path("financial", "loans_credit_definitions.json"),
    LoansAndCredit,
    "Loans and Credit Document Types",
    include_definitions=True,
    include_usage_notes=True
)
# print(f"Loans and Credit Classification Prompt:\n{loan_and_credits_type_classification_prompt}\n" + "="*80)

# Investment and Retirement - Enhanced with JSON knowledge base
investment_and_retirement_classification_prompt = create_enhanced_prompt(
    get_glossary_path("financial", "investments_retirement_definitions.json"),
    InvestmentsAndRetirement,
    "Investment and Retirement Document Types",
    include_definitions=True,
    include_usage_notes=True
)
# print(f"Investment and Retirement Classification Prompt:\n{investment_and_retirement_classification_prompt}\n" + "="*80)

# Income and Benefits - Enhanced with JSON knowledge base
income_nad_benefits_classification_prompt = create_enhanced_prompt(
    get_glossary_path("financial", "income_benefits_definitions.json"),
    IncomeAndBenefitsFinancial,
    "Income and Benefits Document Types",
    include_definitions=True,
    include_usage_notes=True
)
# print(f"Income and Benefits Classification Prompt:\n{income_nad_benefits_classification_prompt}\n" + "="*80)

# Billing, Invoices and Payments - Enhanced with JSON knowledge base
billing_invoices_payments_classification_prompt = create_enhanced_prompt(
    get_glossary_path("financial", "billing_invoices_payments_definitions.json"),
    BillingInvoicesPayments,
    "Billing, Invoices and Payments Document Types",
    billing_docs_descriptions,
    include_definitions=True,
    include_usage_notes=True
)
# print(f"Billing, Invoices and Payments Classification Prompt:\n{billing_invoices_payments_classification_prompt}\n" + "="*80)

# Property and Asset Management - Enhanced with JSON knowledge base
property_and_asset_management_classification_prompt = create_enhanced_prompt(
    get_glossary_path("financial", "property_asset_management_definitions.json"),
    PropertyAndAssetManagement,
    "Property and Asset Management Document Types",
    include_definitions=True,
    include_usage_notes=True
)
# print(f"Property and Asset Management Classification Prompt:\n{property_and_asset_management_classification_prompt}\n" + "="*80)


# =============================================================================
# INSURANCE DOCUMENT CLASSIFICATION PROMPTS
# =============================================================================

# Insurance Document Types - Enhanced with JSON knowledge base
insurance_type_classification_prompt = create_enhanced_prompt(
    get_glossary_path("insurance", "insurance_subtype_definitions.json"),
    InsuranceSubType,
    "Insurance Document Types",
    insurance_subtype_descriptions,
    include_definitions=True,
    include_usage_notes=True
)
# print(f"Insurance Type Classification Prompt:\n{insurance_type_classification_prompt}\n" + "="*80)

# Policy Details and Coverage - Using enum (subcategory of insurance JSON)
policy_details_and_coverage_classification_prompt = generate_category_prompt(
    base_non_court_classification_prompt, 
    PolicyDetailsAndCoverage, 
    "Policy Details and Coverage Document Types"
)
# print(f"Policy Details and Coverage Classification Prompt:\n{policy_details_and_coverage_classification_prompt}\n" + "="*80)

# Claims and Communication - Using enum (subcategory of insurance JSON)
claims_and_communication = generate_category_prompt(
    base_non_court_classification_prompt, 
    ClaimsProcessAndCommunication, 
    "Claims and Communication Document Types"
)
# print(f"Claims and Communication Classification Prompt:\n{claims_and_communication}\n" + "="*80)


# =============================================================================
# MEDICAL DOCUMENT CLASSIFICATION PROMPTS
# =============================================================================

# Medical Document Types - Enhanced with JSON overview
try:
    medical_type_classification_prompt = generate_enhanced_category_prompt(
        base_non_court_classification_prompt,
        get_glossary_path("medical", "health_medical_overview.json"),
        "Medical Document Types",
        include_definitions=True,
        include_usage_notes=False  # Overview file, skip detailed usage notes
    )
except Exception:
    medical_type_classification_prompt = generate_category_prompt(
        base_non_court_classification_prompt, 
        HealthMedicalSubType, 
        "Medical Document Types", 
        health_medical_subtype_descriptions
    )
# print(f"Medical Type Classification Prompt:\n{medical_type_classification_prompt}\n" + "="*80)

# Government Benefit Administration - Enhanced with JSON knowledge base
gov_benefit_administration_classification_prompt = create_enhanced_prompt(
    get_glossary_path("medical", "government_benefit_administration_definitions.json"),
    GovernmentBenefitAdministration,
    "Government Benefit Administration Document Types",
    include_definitions=True,
    include_usage_notes=True
)
# print(f"Government Benefit Administration Classification Prompt:\n{gov_benefit_administration_classification_prompt}\n" + "="*80)

# Medical Records and Evaluations - Enhanced with JSON knowledge base
medical_records_evaluation_prompt = create_enhanced_prompt(
    get_glossary_path("medical", "medical_records_evaluations_definitions.json"),
    MedicalRecordsAndEvaluations,
    "Medical Records Evaluation Document Types",
    include_definitions=True,
    include_usage_notes=True
)
# print(f"Medical Records Evaluation Classification Prompt:\n{medical_records_evaluation_prompt}\n" + "="*80)

# Healthcare Finance and Claims - Enhanced with JSON knowledge base
healthcare_finance_claims_prompt = create_enhanced_prompt(
    get_glossary_path("medical", "healthcare_finance_claims_definitions.json"),
    HealthcareFinanceAndClaims,
    "Healthcare Finance Claims Document Types",
    include_definitions=True,
    include_usage_notes=True,
    max_definitions=20  # Limit due to large number of definitions
)
# print(f"Healthcare Finance Claims Classification Prompt:\n{healthcare_finance_claims_prompt}\n" + "="*80)


# =============================================================================
# GOVERNMENT AND ADMINISTRATIVE DOCUMENT CLASSIFICATION PROMPTS
# =============================================================================

# Government and Agency Documents - Enhanced with JSON knowledge base
government_agency_classification_prompt = create_enhanced_prompt(
    get_glossary_path("government_and_agency", "government_agency_documents_definitions.json"),
    GovernmentAndAgencyDocuments,
    "Government Agency Document Types",
    include_definitions=True,
    include_usage_notes=True
)
# print(f"Government Agency Classification Prompt:\n{government_agency_classification_prompt}\n" + "="*80)


# =============================================================================
# CASE AND CLAIM MANAGEMENT DOCUMENT CLASSIFICATION PROMPTS
# =============================================================================

# Case and Claim Management - Enhanced with JSON overview
try:
    case_claim_management_classification_prompt = generate_enhanced_category_prompt(
        base_non_court_classification_prompt,
        get_glossary_path("case_claim", "case_claim_overview.json"),
        "Case and Claim Management Document Types",
        include_definitions=True,
        include_usage_notes=False  # Overview file
    )
except Exception:
    case_claim_management_classification_prompt = generate_category_prompt(
        base_non_court_classification_prompt, 
        CaseClaimSubType, 
        "Case and Claim Management Document Types", 
        case_claim_subtype_descriptions
    )
# print(f"Case and Claim Management Classification Prompt:\n{case_claim_management_classification_prompt}\n" + "="*80)

# Client Profile - Using enum (part of case claim overview)
client_profile_classification_prompt = generate_category_prompt(
    base_non_court_classification_prompt, 
    ClientIntakeAndProfile, 
    "Client Profile Document Types"
)
# print(f"Client Profile Classification Prompt:\n{client_profile_classification_prompt}\n" + "="*80)

# Case Management and Workflow - Enhanced with JSON knowledge base
case_management_classification_prompt = create_enhanced_prompt(
    get_glossary_path("case_claim", "case_management_workflow_definitions.json"),
    CaseManagementAndWorkflow,
    "Case Management Document Types",
    include_definitions=True,
    include_usage_notes=True
)
# print(f"Case Management Classification Prompt:\n{case_management_classification_prompt}\n" + "="*80)

# Claims, Resolutions and Agreements - Enhanced with JSON knowledge base
case_resolution_classification_prompt = create_enhanced_prompt(
    get_glossary_path("case_claim", "claims_resolutions_agreements_definitions.json"),
    ClaimsResolutionsAndAgreements,
    "Case Resolution Document Types",
    include_definitions=True,
    include_usage_notes=True
)
# print(f"Case Resolution Classification Prompt:\n{case_resolution_classification_prompt}\n" + "="*80)

# Formal Legal and Administrative - Enhanced with JSON knowledge base
formal_legal_admin_prompt = create_enhanced_prompt(
    get_glossary_path("case_claim", "formal_legal_specialized_admin_definitions.json"),
    FormalLegalAndSpecializedAdmin,
    "Formal Legal and Administrative Document Types",
    include_definitions=True,
    include_usage_notes=True,
    max_definitions=15  # Limit due to large number of definitions
)
# print(f"Formal Legal and Administrative Classification Prompt:\n{formal_legal_admin_prompt}\n" + "="*80)


# =============================================================================
# GENERAL CORRESPONDENCE DOCUMENT CLASSIFICATION PROMPTS
# =============================================================================

# General Communication and Administrative - Enhanced with JSON knowledge base
general_communication_admin_classification_prompt = create_enhanced_prompt(
    get_glossary_path("general_correspondance", "general_communication_administrative_definitions.json"),
    GeneralCommunicationAndAdministrativeDocuments,
    "General Communication and Administrative Document Types",
    include_definitions=True,
    include_usage_notes=True
)
# print(f"General Communication and Administrative Classification Prompt:\n{general_communication_admin_classification_prompt}\n" + "="*80)


# =============================================================================
# NOTICE DOCUMENT CLASSIFICATION PROMPTS
# =============================================================================

# Notice Documents - Enhanced with JSON knowledge base
non_court_notice_classification_prompt = create_enhanced_prompt(
    get_glossary_path("notices", "notice_documents_definitions.json"),
    NoticeDocuments,
    "Notice Document Types",
    include_definitions=True,
    include_usage_notes=True
)
# print(f"Notice Documents Classification Prompt:\n{non_court_notice_classification_prompt}\n" + "="*80)


# =============================================================================
# SUMMARY AND VALIDATION
# =============================================================================

def print_all_prompts():
    """Print all generated prompts for review."""
    prompts = [
        ("General Type", general_type_classification_prompt),
        ("Financial Type", financial_type_classification_prompt),
        ("Banking and Accounting", banking_and_accounting_classification_prompt),
        ("Loans and Credit", loan_and_credits_type_classification_prompt),
        ("Investment and Retirement", investment_and_retirement_classification_prompt),
        ("Income and Benefits", income_nad_benefits_classification_prompt),
        ("Billing, Invoices and Payments", billing_invoices_payments_classification_prompt),
        ("Property and Asset Management", property_and_asset_management_classification_prompt),
        ("Insurance Type", insurance_type_classification_prompt),
        ("Policy Details and Coverage", policy_details_and_coverage_classification_prompt),
        ("Claims and Communication", claims_and_communication),
        ("Medical Type", medical_type_classification_prompt),
        ("Government Benefit Administration", gov_benefit_administration_classification_prompt),
        ("Medical Records Evaluation", medical_records_evaluation_prompt),
        ("Healthcare Finance Claims", healthcare_finance_claims_prompt),
        ("Government Agency", government_agency_classification_prompt),
        ("Case and Claim Management", case_claim_management_classification_prompt),
        ("Client Profile", client_profile_classification_prompt),
        ("Case Management", case_management_classification_prompt),
        ("Case Resolution", case_resolution_classification_prompt),
        ("Formal Legal and Administrative", formal_legal_admin_prompt),
        ("General Communication and Administrative", general_communication_admin_classification_prompt),
        ("Notice Documents", non_court_notice_classification_prompt),
    ]
    
    for name, prompt in prompts:
        print(f"\n{'='*80}")
        print(f"{name} Classification Prompt:")
        print(f"{'='*80}")
        print(prompt)
        print(f"\nPrompt length: {len(prompt)} characters")


if __name__ == "__main__":
    print("Enhanced Prompt Factory - All prompts generated successfully!")
    print(f"Total prompts: 23")
    
    # Uncomment to print all prompts:
    # print_all_prompts()
