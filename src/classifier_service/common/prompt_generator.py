from typing import Callable, Optional, List, Any, Union, Dict
from enum import Enum<PERSON>eta
from pathlib import Path
from src.shared.utils.logger_config import setup_logger
from src.classifier_service.common.knowledge_loader import KnowledgeLoader

logger = setup_logger(__name__)

def generate_category_prompt(
    base_prompt: Callable[[List[str], Optional[str], Optional[str]], str],
    category: EnumMeta,
    category_name: Optional[str] = None,
    category_description: Optional[str] = None
) -> str:
    """
    Generates a prompt using a base_prompt function and an enum category.

    Note: This function maintains backward compatibility. For enhanced functionality
    with JSON knowledge bases, use generate_enhanced_category_prompt.
    """
    if not callable(base_prompt):
        raise TypeError("base_prompt must be a callable")

    if not isinstance(category, EnumMeta):
        raise ValueError("category must be an Enum type")

    try:
        options = [cat.value for cat in category]
    except Exception as e:
        logger.exception("Failed to extract values from Enum category")
        raise ValueError("Invalid category Enum values") from e

    if not options:
        raise ValueError("category must contain at least one option")

    try:
        prompt = base_prompt(options, category_name, category_description)
        # logger.debug(f"Generated category prompt: {prompt}")
        return prompt
    except Exception as e:
        logger.exception("Failed to generate category prompt")
        raise


def generate_enhanced_category_prompt(
    base_prompt: Callable[[List[str], Optional[str], Optional[str]], str],
    knowledge_source: Union[EnumMeta, str, Path, Dict],
    category_name: Optional[str] = None,
    include_definitions: bool = True,
    include_usage_notes: bool = False,
    max_definitions: int = None
) -> str:
    """
    Generate prompt with enhanced knowledge support from various sources.

    Args:
        base_prompt: Function to generate the base prompt
        knowledge_source: Knowledge source - can be:
            - EnumMeta: Python enum class
            - str: JSON file path or JSON string
            - Path: JSON file path
            - Dict: JSON data dictionary
        category_name: Optional category name override
        include_definitions: Whether to include detailed definitions
        include_usage_notes: Whether to include usage guidelines
        max_definitions: Maximum number of definitions to include

    Returns:
        Generated prompt string with enhanced knowledge

    Raises:
        TypeError: If base_prompt is not callable
        ValueError: If knowledge source is invalid
    """
    if not callable(base_prompt):
        raise TypeError("base_prompt must be a callable")

    try:
        # Load knowledge from source
        knowledge = KnowledgeLoader.load_category_knowledge(knowledge_source)

        # Build enhanced description
        category_description = knowledge['description']

        # Add detailed definitions if requested
        if include_definitions and knowledge['definitions']:
            definitions_text = KnowledgeLoader.format_definitions_for_prompt(
                knowledge['definitions'],
                max_definitions=max_definitions
            )
            category_description += definitions_text

        # Add usage notes if requested
        if include_usage_notes and knowledge['usage_notes']:
            usage_text = KnowledgeLoader.get_usage_notes_text(knowledge['usage_notes'])
            category_description += usage_text

        # Generate prompt
        prompt = base_prompt(knowledge['options'], category_name, category_description)
        logger.debug(f"Generated enhanced category prompt with {len(knowledge['options'])} options")
        return prompt

    except Exception as e:
        logger.exception("Failed to generate enhanced category prompt")
        raise


def generate_correction_prompt(
    base_prompt: Callable[[Any, str], str],
    category: Any,
    state: dict
) -> str:
    """
    Generates a correction prompt using a classification result from state.
    """
    if not isinstance(state, dict):
        raise TypeError("state must be a dictionary")

    if not callable(base_prompt):
        raise TypeError("base_prompt must be a callable")

    try:
        doc_type = state["classification_result"]
    except KeyError as e:
        logger.error("Missing 'classification_result' in state")
        raise KeyError("Missing 'classification_result' in state") from e

    try:
        prompt = base_prompt(category, doc_type)
        # logger.debug(f"Generated correction prompt: {prompt}")
        return prompt
    except Exception as e:
        logger.exception("Failed to generate correction prompt")
        raise
