import json
from pathlib import Path
from typing import Union, Dict, Any, List
from enum import EnumMeta
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)


class KnowledgeLoader:
    """Utility class for loading classification knowledge from various sources."""
    
    @staticmethod
    def load_category_knowledge(source: Union[EnumMeta, str, Path, Dict]) -> Dict[str, Any]:
        """
        Load category knowledge from various sources.
        
        Args:
            source: Knowledge source - can be:
                - EnumMeta: Python enum class
                - str: JSON file path or JSON string
                - Path: JSON file path
                - Dict: JSON data dictionary
        
        Returns:
            Dict containing standardized knowledge structure with:
                - options: List of category options
                - description: Category description
                - definitions: Detailed definitions dictionary
                - usage_notes: Usage notes and guidelines
        
        Raises:
            ValueError: If source type is unsupported or JSON structure is invalid
            FileNotFoundError: If JSON file path doesn't exist
            json.JSONDecodeError: If JSON string/file is malformed
        """
        try:
            if isinstance(source, EnumMeta):
                return KnowledgeLoader._process_enum(source)
            elif isinstance(source, dict):
                return KnowledgeLoader._process_json_data(source)
            elif isinstance(source, (str, Path)):
                return KnowledgeLoader._process_file_or_string(source)
            else:
                raise ValueError(f"Unsupported knowledge source type: {type(source)}")
        except Exception as e:
            logger.exception(f"Failed to load category knowledge from source: {source}")
            raise
    
    @staticmethod
    def _process_enum(enum_class: EnumMeta) -> Dict[str, Any]:
        """Process Python enum into standardized knowledge format."""
        try:
            options = [cat.value for cat in enum_class]
            return {
                'options': options,
                'description': enum_class.__doc__ or f"Enum representing {enum_class.__name__} categories",
                'definitions': {},
                'usage_notes': {},
                'source_type': 'enum'
            }
        except Exception as e:
            logger.exception(f"Failed to process enum: {enum_class}")
            raise ValueError(f"Invalid enum class: {enum_class}") from e
    
    @staticmethod
    def _process_file_or_string(source: Union[str, Path]) -> Dict[str, Any]:
        """Process file path or JSON string into standardized knowledge format."""
        try:
            # Determine if it's a file path or JSON string
            source_str = str(source)
            if source_str.endswith('.json') or isinstance(source, Path):
                # It's a file path
                file_path = Path(source)
                if not file_path.exists():
                    raise FileNotFoundError(f"JSON file not found: {file_path}")
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                logger.debug(f"Loaded JSON knowledge from file: {file_path}")
            else:
                # It's a JSON string
                json_data = json.loads(source_str)
                logger.debug("Loaded JSON knowledge from string")
            
            return KnowledgeLoader._process_json_data(json_data)
            
        except json.JSONDecodeError as e:
            logger.exception(f"Invalid JSON format in source: {source}")
            raise json.JSONDecodeError(f"Invalid JSON format: {e}", e.doc, e.pos) from e
        except Exception as e:
            logger.exception(f"Failed to process file or string: {source}")
            raise
    
    @staticmethod
    def _process_json_data(json_data: Dict) -> Dict[str, Any]:
        """Process JSON data into standardized knowledge format."""
        try:
            # Extract options based on JSON structure
            if 'document_types' in json_data:
                options = list(json_data['document_types'].keys())
                definitions = json_data['document_types']
                knowledge_type = 'document_types'
            elif 'subcategories' in json_data:
                options = list(json_data['subcategories'].keys())
                definitions = json_data['subcategories']
                knowledge_type = 'subcategories'
            else:
                # Try to find any top-level dictionary that might contain definitions
                potential_keys = [k for k, v in json_data.items() 
                                if isinstance(v, dict) and k not in ['usage_notes', 'classification_guidelines']]
                if potential_keys:
                    key = potential_keys[0]
                    options = list(json_data[key].keys())
                    definitions = json_data[key]
                    knowledge_type = key
                    logger.warning(f"Using '{key}' as definitions source - consider using 'document_types' or 'subcategories'")
                else:
                    raise ValueError("JSON must contain 'document_types', 'subcategories', or other definition dictionary")
            
            return {
                'options': options,
                'description': json_data.get('description', ''),
                'definitions': definitions,
                'usage_notes': json_data.get('usage_notes', {}),
                'classification_guidelines': json_data.get('classification_guidelines', {}),
                'source_type': 'json',
                'knowledge_type': knowledge_type
            }
            
        except Exception as e:
            logger.exception("Failed to process JSON data structure")
            raise ValueError(f"Invalid JSON knowledge structure: {e}") from e
    
    @staticmethod
    def format_definitions_for_prompt(
        definitions: Dict[str, Any], 
        include_characteristics: bool = True,
        include_distinguishing_features: bool = True,
        max_definitions: int = None
    ) -> str:
        """
        Format definitions dictionary into a readable string for prompts.
        
        Args:
            definitions: Dictionary of definitions
            include_characteristics: Whether to include key_characteristics
            include_distinguishing_features: Whether to include distinguishing_features
            max_definitions: Maximum number of definitions to include (None for all)
        
        Returns:
            Formatted string with definitions
        """
        if not definitions:
            return ""
        
        formatted_text = "\n\nDetailed Definitions:\n"
        
        items = list(definitions.items())
        if max_definitions:
            items = items[:max_definitions]
        
        for item_name, details in items:
            if isinstance(details, dict):
                definition = details.get('definition', details.get('description', 'No definition available'))
                formatted_text += f"\n{item_name}: {definition}"
                
                if include_characteristics and 'key_characteristics' in details:
                    characteristics = details['key_characteristics']
                    if isinstance(characteristics, list):
                        formatted_text += f"\n  Key characteristics: {', '.join(characteristics)}"
                
                if include_distinguishing_features and 'distinguishing_features' in details:
                    features = details['distinguishing_features']
                    formatted_text += f"\n  Distinguishing features: {features}"
                
                formatted_text += "\n"
            else:
                formatted_text += f"\n{item_name}: {details}\n"
        
        return formatted_text
    
    @staticmethod
    def get_usage_notes_text(usage_notes: Dict[str, Any]) -> str:
        """Format usage notes into readable text for prompts."""
        if not usage_notes:
            return ""
        
        text = "\n\nUsage Guidelines:\n"
        
        if 'key_distinctions' in usage_notes:
            text += "\nKey Distinctions:\n"
            distinctions = usage_notes['key_distinctions']
            if isinstance(distinctions, dict):
                for key, value in distinctions.items():
                    text += f"- {key}: {value}\n"
            else:
                text += f"{distinctions}\n"
        
        if 'classification_guidelines' in usage_notes:
            text += "\nClassification Guidelines:\n"
            guidelines = usage_notes['classification_guidelines']
            if isinstance(guidelines, dict):
                for key, value in guidelines.items():
                    text += f"- {key}: {value}\n"
            else:
                text += f"{guidelines}\n"
        
        return text


def load_knowledge_base(file_path: Union[str, Path]) -> Dict[str, Any]:
    """
    Convenience function to load a JSON knowledge base file.
    
    Args:
        file_path: Path to JSON knowledge base file
    
    Returns:
        Loaded knowledge base dictionary
    """
    return KnowledgeLoader.load_category_knowledge(file_path)


def get_glossary_path(category: str, filename: str = None) -> Path:
    """
    Get the path to a glossary file for a specific category.
    
    Args:
        category: Category name (e.g., 'financial', 'medical', 'insurance')
        filename: Specific filename (if None, returns directory path)
    
    Returns:
        Path to glossary file or directory
    """
    base_path = Path(__file__).parent.parent / "non_court_docs" / "glossary" / category
    
    if filename:
        return base_path / filename
    return base_path
