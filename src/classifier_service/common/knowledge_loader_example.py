"""
Example usage of the KnowledgeLoader and enhanced prompt generation.

This script demonstrates how to use the new knowledge loading capabilities
with both Python enums and JSON knowledge base files.
"""

from src.classifier_service.common.knowledge_loader import KnowledgeLoader, get_glossary_path
from src.classifier_service.common.prompt_generator import generate_enhanced_category_prompt
from src.classifier_service.non_court_docs.consts.financial import BankingAndAccounts
from src.classifier_service.non_court_docs.prompts.base_prompts import base_non_court_classification_prompt


def example_enum_usage():
    """Example of loading knowledge from a Python enum."""
    print("=== Example: Loading from Python Enum ===")
    
    # Load knowledge from enum
    knowledge = KnowledgeLoader.load_category_knowledge(BankingAndAccounts)
    
    print(f"Options: {knowledge['options']}")
    print(f"Description: {knowledge['description']}")
    print(f"Source type: {knowledge['source_type']}")
    print()


def example_json_usage():
    """Example of loading knowledge from JSON file."""
    print("=== Example: Loading from JSON Knowledge Base ===")
    
    try:
        # Load knowledge from JSON file
        json_path = get_glossary_path("financial", "banking_accounts_definitions.json")
        knowledge = KnowledgeLoader.load_category_knowledge(json_path)
        
        print(f"Options: {knowledge['options'][:3]}...")  # Show first 3
        print(f"Description: {knowledge['description']}")
        print(f"Source type: {knowledge['source_type']}")
        print(f"Knowledge type: {knowledge['knowledge_type']}")
        print(f"Number of definitions: {len(knowledge['definitions'])}")
        
        # Show one definition example
        if knowledge['definitions']:
            first_key = list(knowledge['definitions'].keys())[0]
            first_def = knowledge['definitions'][first_key]
            print(f"\nExample definition for '{first_key}':")
            print(f"  Definition: {first_def.get('definition', 'N/A')}")
            print(f"  Key characteristics: {first_def.get('key_characteristics', [])}")
        
    except Exception as e:
        print(f"Error loading JSON knowledge: {e}")
    print()


def example_enhanced_prompt():
    """Example of generating enhanced prompts with detailed definitions."""
    print("=== Example: Enhanced Prompt Generation ===")
    
    try:
        # Generate enhanced prompt with JSON knowledge base
        json_path = get_glossary_path("financial", "banking_accounts_definitions.json")
        
        enhanced_prompt = generate_enhanced_category_prompt(
            base_non_court_classification_prompt,
            json_path,
            "Banking and Accounting Document Types",
            include_definitions=True,
            include_usage_notes=True,
            max_definitions=3  # Limit for example
        )
        
        print("Enhanced prompt generated successfully!")
        print(f"Prompt length: {len(enhanced_prompt)} characters")
        print("\nFirst 500 characters of prompt:")
        print(enhanced_prompt[:500] + "..." if len(enhanced_prompt) > 500 else enhanced_prompt)
        
    except Exception as e:
        print(f"Error generating enhanced prompt: {e}")
    print()


def example_comparison():
    """Example comparing basic vs enhanced prompts."""
    print("=== Example: Basic vs Enhanced Prompt Comparison ===")
    
    try:
        # Basic prompt (enum only)
        from src.classifier_service.common.prompt_generator import generate_category_prompt
        
        basic_prompt = generate_category_prompt(
            base_non_court_classification_prompt,
            BankingAndAccounts,
            "Banking and Accounting Document Types"
        )
        
        # Enhanced prompt (with JSON knowledge)
        json_path = get_glossary_path("financial", "banking_accounts_definitions.json")
        enhanced_prompt = generate_enhanced_category_prompt(
            base_non_court_classification_prompt,
            json_path,
            "Banking and Accounting Document Types",
            include_definitions=True,
            max_definitions=2
        )
        
        print(f"Basic prompt length: {len(basic_prompt)} characters")
        print(f"Enhanced prompt length: {len(enhanced_prompt)} characters")
        print(f"Enhancement adds: {len(enhanced_prompt) - len(basic_prompt)} characters")
        
    except Exception as e:
        print(f"Error in comparison: {e}")
    print()


def main():
    """Run all examples."""
    print("Knowledge Loader Examples\n" + "=" * 50)
    
    example_enum_usage()
    example_json_usage()
    example_enhanced_prompt()
    example_comparison()
    
    print("Examples completed!")


if __name__ == "__main__":
    main()
